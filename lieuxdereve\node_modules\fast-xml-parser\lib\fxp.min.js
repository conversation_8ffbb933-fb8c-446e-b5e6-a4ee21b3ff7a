!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.fxp=e():t.fxp=e()}(this,(()=>(()=>{"use strict";var t={d:(e,r)=>{for(var i in r)t.o(r,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:r[i]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{XMLBuilder:()=>pt,XMLParser:()=>at,XMLValidator:()=>mt});var r=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",i=new RegExp("^["+r+"]["+r+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$");function n(t,e){for(var r=[],i=e.exec(t);i;){var n=[];n.startIndex=e.lastIndex-i[0].length;for(var a=i.length,s=0;s<a;s++)n.push(i[s]);r.push(n),i=e.exec(t)}return r}var a=function(t){return!(null==i.exec(t))},s={allowBooleanAttributes:!1,unpairedTags:[]};function o(t,e){e=Object.assign({},s,e);var r=[],i=!1,n=!1;"\ufeff"===t[0]&&(t=t.substr(1));for(var o=0;o<t.length;o++)if("<"===t[o]&&"?"===t[o+1]){if((o=l(t,o+=2)).err)return o}else{if("<"!==t[o]){if(u(t[o]))continue;return m("InvalidChar","char '"+t[o]+"' is not expected.",b(t,o))}var f=o;if("!"===t[++o]){o=h(t,o);continue}var d=!1;"/"===t[o]&&(d=!0,o++);for(var g="";o<t.length&&">"!==t[o]&&" "!==t[o]&&"\t"!==t[o]&&"\n"!==t[o]&&"\r"!==t[o];o++)g+=t[o];if("/"===(g=g.trim())[g.length-1]&&(g=g.substring(0,g.length-1),o--),!a(g))return m("InvalidTag",0===g.trim().length?"Invalid space after '<'.":"Tag '"+g+"' is an invalid name.",b(t,o));var x=p(t,o);if(!1===x)return m("InvalidAttr","Attributes for '"+g+"' have open quote.",b(t,o));var N=x.value;if(o=x.index,"/"===N[N.length-1]){var E=o-N.length,y=c(N=N.substring(0,N.length-1),e);if(!0!==y)return m(y.err.code,y.err.msg,b(t,E+y.err.line));i=!0}else if(d){if(!x.tagClosed)return m("InvalidTag","Closing tag '"+g+"' doesn't have proper closing.",b(t,o));if(N.trim().length>0)return m("InvalidTag","Closing tag '"+g+"' can't have attributes or invalid starting.",b(t,f));if(0===r.length)return m("InvalidTag","Closing tag '"+g+"' has not been opened.",b(t,f));var T=r.pop();if(g!==T.tagName){var w=b(t,T.tagStartPos);return m("InvalidTag","Expected closing tag '"+T.tagName+"' (opened in line "+w.line+", col "+w.col+") instead of closing tag '"+g+"'.",b(t,f))}0==r.length&&(n=!0)}else{var P=c(N,e);if(!0!==P)return m(P.err.code,P.err.msg,b(t,o-N.length+P.err.line));if(!0===n)return m("InvalidXml","Multiple possible root nodes found.",b(t,o));-1!==e.unpairedTags.indexOf(g)||r.push({tagName:g,tagStartPos:f}),i=!0}for(o++;o<t.length;o++)if("<"===t[o]){if("!"===t[o+1]){o=h(t,++o);continue}if("?"!==t[o+1])break;if((o=l(t,++o)).err)return o}else if("&"===t[o]){var A=v(t,o);if(-1==A)return m("InvalidChar","char '&' is not expected.",b(t,o));o=A}else if(!0===n&&!u(t[o]))return m("InvalidXml","Extra text at the end",b(t,o));"<"===t[o]&&o--}return i?1==r.length?m("InvalidTag","Unclosed tag '"+r[0].tagName+"'.",b(t,r[0].tagStartPos)):!(r.length>0)||m("InvalidXml","Invalid '"+JSON.stringify(r.map((function(t){return t.tagName})),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):m("InvalidXml","Start tag expected.",1)}function u(t){return" "===t||"\t"===t||"\n"===t||"\r"===t}function l(t,e){for(var r=e;e<t.length;e++)if("?"!=t[e]&&" "!=t[e]);else{var i=t.substr(r,e-r);if(e>5&&"xml"===i)return m("InvalidXml","XML declaration allowed only at the start of the document.",b(t,e));if("?"==t[e]&&">"==t[e+1]){e++;break}}return e}function h(t,e){if(t.length>e+5&&"-"===t[e+1]&&"-"===t[e+2]){for(e+=3;e<t.length;e++)if("-"===t[e]&&"-"===t[e+1]&&">"===t[e+2]){e+=2;break}}else if(t.length>e+8&&"D"===t[e+1]&&"O"===t[e+2]&&"C"===t[e+3]&&"T"===t[e+4]&&"Y"===t[e+5]&&"P"===t[e+6]&&"E"===t[e+7]){var r=1;for(e+=8;e<t.length;e++)if("<"===t[e])r++;else if(">"===t[e]&&0==--r)break}else if(t.length>e+9&&"["===t[e+1]&&"C"===t[e+2]&&"D"===t[e+3]&&"A"===t[e+4]&&"T"===t[e+5]&&"A"===t[e+6]&&"["===t[e+7])for(e+=8;e<t.length;e++)if("]"===t[e]&&"]"===t[e+1]&&">"===t[e+2]){e+=2;break}return e}var f='"',d="'";function p(t,e){for(var r="",i="",n=!1;e<t.length;e++){if(t[e]===f||t[e]===d)""===i?i=t[e]:i!==t[e]||(i="");else if(">"===t[e]&&""===i){n=!0;break}r+=t[e]}return""===i&&{value:r,index:e,tagClosed:n}}var g=new RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function c(t,e){for(var r=n(t,g),i={},a=0;a<r.length;a++){if(0===r[a][1].length)return m("InvalidAttr","Attribute '"+r[a][2]+"' has no space in starting.",N(r[a]));if(void 0!==r[a][3]&&void 0===r[a][4])return m("InvalidAttr","Attribute '"+r[a][2]+"' is without value.",N(r[a]));if(void 0===r[a][3]&&!e.allowBooleanAttributes)return m("InvalidAttr","boolean attribute '"+r[a][2]+"' is not allowed.",N(r[a]));var s=r[a][2];if(!x(s))return m("InvalidAttr","Attribute '"+s+"' is an invalid name.",N(r[a]));if(i.hasOwnProperty(s))return m("InvalidAttr","Attribute '"+s+"' is repeated.",N(r[a]));i[s]=1}return!0}function v(t,e){if(";"===t[++e])return-1;if("#"===t[e])return function(t,e){var r=/\d/;for("x"===t[e]&&(e++,r=/[\da-fA-F]/);e<t.length;e++){if(";"===t[e])return e;if(!t[e].match(r))break}return-1}(t,++e);for(var r=0;e<t.length;e++,r++)if(!(t[e].match(/\w/)&&r<20)){if(";"===t[e])break;return-1}return e}function m(t,e,r){return{err:{code:t,msg:e,line:r.line||r,col:r.col}}}function x(t){return a(t)}function b(t,e){var r=t.substring(0,e).split(/\r?\n/);return{line:r.length,col:r[r.length-1].length+1}}function N(t){return t.startIndex+t[1].length}var E,y={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(t,e){return e},attributeValueProcessor:function(t,e){return e},stopNodes:[],alwaysCreateTextNode:!1,isArray:function(){return!1},commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(t,e,r){return t},captureMetaData:!1};E="function"!=typeof Symbol?"@@xmlMetadata":Symbol("XML Node Metadata");var T=function(){function t(t){this.tagname=t,this.child=[],this[":@"]={}}var e=t.prototype;return e.add=function(t,e){var r;"__proto__"===t&&(t="#__proto__"),this.child.push(((r={})[t]=e,r))},e.addChild=function(t,e){var r,i;"__proto__"===t.tagname&&(t.tagname="#__proto__"),t[":@"]&&Object.keys(t[":@"]).length>0?this.child.push(((r={})[t.tagname]=t.child,r[":@"]=t[":@"],r)):this.child.push(((i={})[t.tagname]=t.child,i)),void 0!==e&&(this.child[this.child.length-1][E]={startIndex:e})},t.getMetaDataSymbol=function(){return E},t}();function w(t,e){var r={};if("O"!==t[e+3]||"C"!==t[e+4]||"T"!==t[e+5]||"Y"!==t[e+6]||"P"!==t[e+7]||"E"!==t[e+8])throw new Error("Invalid Tag instead of DOCTYPE");e+=9;for(var i=1,n=!1,a=!1;e<t.length;e++)if("<"!==t[e]||a)if(">"===t[e]){if(a?"-"===t[e-1]&&"-"===t[e-2]&&(a=!1,i--):i--,0===i)break}else"["===t[e]?n=!0:t[e];else{if(n&&C(t,"!ENTITY",e)){var s,o=void 0,u=A(t,(e+=7)+1);s=u[0],o=u[1],e=u[2],-1===o.indexOf("&")&&(r[s]={regx:RegExp("&"+s+";","g"),val:o})}else if(n&&C(t,"!ELEMENT",e))e=S(t,(e+=8)+1).index;else if(n&&C(t,"!ATTLIST",e))e+=8;else if(n&&C(t,"!NOTATION",e))e=O(t,(e+=9)+1).index;else{if(!C(t,"!--",e))throw new Error("Invalid DOCTYPE");a=!0}i++}if(0!==i)throw new Error("Unclosed DOCTYPE");return{entities:r,i:e}}var P=function(t,e){for(;e<t.length&&/\s/.test(t[e]);)e++;return e};function A(t,e){e=P(t,e);for(var r="";e<t.length&&!/\s/.test(t[e])&&'"'!==t[e]&&"'"!==t[e];)r+=t[e],e++;if(j(r),e=P(t,e),"SYSTEM"===t.substring(e,e+6).toUpperCase())throw new Error("External entities are not supported");if("%"===t[e])throw new Error("Parameter entities are not supported");var i=I(t,e,"entity");return e=i[0],[r,i[1],--e]}function O(t,e){e=P(t,e);for(var r="";e<t.length&&!/\s/.test(t[e]);)r+=t[e],e++;j(r),e=P(t,e);var i=t.substring(e,e+6).toUpperCase();if("SYSTEM"!==i&&"PUBLIC"!==i)throw new Error('Expected SYSTEM or PUBLIC, found "'+i+'"');e+=i.length,e=P(t,e);var n=null,a=null;if("PUBLIC"===i){var s=I(t,e,"publicIdentifier");if(e=s[0],n=s[1],'"'===t[e=P(t,e)]||"'"===t[e]){var o=I(t,e,"systemIdentifier");e=o[0],a=o[1]}}else if("SYSTEM"===i){var u=I(t,e,"systemIdentifier");if(e=u[0],!(a=u[1]))throw new Error("Missing mandatory system identifier for SYSTEM notation")}return{notationName:r,publicIdentifier:n,systemIdentifier:a,index:--e}}function I(t,e,r){var i="",n=t[e];if('"'!==n&&"'"!==n)throw new Error('Expected quoted string, found "'+n+'"');for(e++;e<t.length&&t[e]!==n;)i+=t[e],e++;if(t[e]!==n)throw new Error("Unterminated "+r+" value");return[++e,i]}function S(t,e){e=P(t,e);for(var r="";e<t.length&&!/\s/.test(t[e]);)r+=t[e],e++;if(!j(r))throw new Error('Invalid element name: "'+r+'"');var i="";if("E"===t[e=P(t,e)]&&C(t,"MPTY",e))e+=4;else if("A"===t[e]&&C(t,"NY",e))e+=2;else{if("("!==t[e])throw new Error('Invalid Element Expression, found "'+t[e]+'"');for(e++;e<t.length&&")"!==t[e];)i+=t[e],e++;if(")"!==t[e])throw new Error("Unterminated content model")}return{elementName:r,contentModel:i.trim(),index:e}}function C(t,e,r){for(var i=0;i<e.length;i++)if(e[i]!==t[r+i+1])return!1;return!0}function j(t){if(a(t))return t;throw new Error("Invalid entity name "+t)}const D=/^[-+]?0x[a-fA-F0-9]+$/,V=/^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/,M={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};const _=/^([-+])?(0*)(\d*(\.\d*)?[eE][-\+]?\d+)$/;function k(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=Array(e);r<e;r++)i[r]=t[r];return i}function F(t){return"function"==typeof t?t:Array.isArray(t)?function(e){for(var r,i=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return k(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?k(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(t);!(r=i()).done;){var n=r.value;if("string"==typeof n&&e===n)return!0;if(n instanceof RegExp&&n.test(e))return!0}}:function(){return!1}}var L=function(t){this.options=t,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"¢"},pound:{regex:/&(pound|#163);/g,val:"£"},yen:{regex:/&(yen|#165);/g,val:"¥"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"©"},reg:{regex:/&(reg|#174);/g,val:"®"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:function(t,e){return String.fromCodePoint(Number.parseInt(e,10))}},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:function(t,e){return String.fromCodePoint(Number.parseInt(e,16))}}},this.addExternalEntities=B,this.parseXml=R,this.parseTextData=U,this.resolveNameSpace=G,this.buildAttributesMap=Y,this.isItStopNode=W,this.replaceEntitiesValue=q,this.readStopNodeData=H,this.saveTextToParentTag=Z,this.addChild=$,this.ignoreAttributesFn=F(this.options.ignoreAttributes)};function B(t){for(var e=Object.keys(t),r=0;r<e.length;r++){var i=e[r];this.lastEntities[i]={regex:new RegExp("&"+i+";","g"),val:t[i]}}}function U(t,e,r,i,n,a,s){if(void 0!==t&&(this.options.trimValues&&!i&&(t=t.trim()),t.length>0)){s||(t=this.replaceEntitiesValue(t));var o=this.options.tagValueProcessor(e,t,r,n,a);return null==o?t:typeof o!=typeof t||o!==t?o:this.options.trimValues||t.trim()===t?K(t,this.options.parseTagValue,this.options.numberParseOptions):t}}function G(t){if(this.options.removeNSPrefix){var e=t.split(":"),r="/"===t.charAt(0)?"/":"";if("xmlns"===e[0])return"";2===e.length&&(t=r+e[1])}return t}var X=new RegExp("([^\\s=]+)\\s*(=\\s*(['\"])([\\s\\S]*?)\\3)?","gm");function Y(t,e,r){if(!0!==this.options.ignoreAttributes&&"string"==typeof t){for(var i=n(t,X),a=i.length,s={},o=0;o<a;o++){var u=this.resolveNameSpace(i[o][1]);if(!this.ignoreAttributesFn(u,e)){var l=i[o][4],h=this.options.attributeNamePrefix+u;if(u.length)if(this.options.transformAttributeName&&(h=this.options.transformAttributeName(h)),"__proto__"===h&&(h="#__proto__"),void 0!==l){this.options.trimValues&&(l=l.trim()),l=this.replaceEntitiesValue(l);var f=this.options.attributeValueProcessor(u,l,e);s[h]=null==f?l:typeof f!=typeof l||f!==l?f:K(l,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(s[h]=!0)}}if(!Object.keys(s).length)return;if(this.options.attributesGroupName){var d={};return d[this.options.attributesGroupName]=s,d}return s}}var R=function(t){t=t.replace(/\r\n?/g,"\n");for(var e=new T("!xml"),r=e,i="",n="",a=0;a<t.length;a++)if("<"===t[a])if("/"===t[a+1]){var s=z(t,">",a,"Closing Tag is not closed."),o=t.substring(a+2,s).trim();if(this.options.removeNSPrefix){var u=o.indexOf(":");-1!==u&&(o=o.substr(u+1))}this.options.transformTagName&&(o=this.options.transformTagName(o)),r&&(i=this.saveTextToParentTag(i,r,n));var l=n.substring(n.lastIndexOf(".")+1);if(o&&-1!==this.options.unpairedTags.indexOf(o))throw new Error("Unpaired tag can not be used as closing tag: </"+o+">");var h=0;l&&-1!==this.options.unpairedTags.indexOf(l)?(h=n.lastIndexOf(".",n.lastIndexOf(".")-1),this.tagsNodeStack.pop()):h=n.lastIndexOf("."),n=n.substring(0,h),r=this.tagsNodeStack.pop(),i="",a=s}else if("?"===t[a+1]){var f=J(t,a,!1,"?>");if(!f)throw new Error("Pi Tag is not closed.");if(i=this.saveTextToParentTag(i,r,n),this.options.ignoreDeclaration&&"?xml"===f.tagName||this.options.ignorePiTags);else{var d=new T(f.tagName);d.add(this.options.textNodeName,""),f.tagName!==f.tagExp&&f.attrExpPresent&&(d[":@"]=this.buildAttributesMap(f.tagExp,n,f.tagName)),this.addChild(r,d,n,a)}a=f.closeIndex+1}else if("!--"===t.substr(a+1,3)){var p=z(t,"--\x3e",a+4,"Comment is not closed.");if(this.options.commentPropName){var g,c=t.substring(a+4,p-2);i=this.saveTextToParentTag(i,r,n),r.add(this.options.commentPropName,[(g={},g[this.options.textNodeName]=c,g)])}a=p}else if("!D"===t.substr(a+1,2)){var v=w(t,a);this.docTypeEntities=v.entities,a=v.i}else if("!["===t.substr(a+1,2)){var m=z(t,"]]>",a,"CDATA is not closed.")-2,x=t.substring(a+9,m);i=this.saveTextToParentTag(i,r,n);var b,N=this.parseTextData(x,r.tagname,n,!0,!1,!0,!0);null==N&&(N=""),this.options.cdataPropName?r.add(this.options.cdataPropName,[(b={},b[this.options.textNodeName]=x,b)]):r.add(this.options.textNodeName,N),a=m+2}else{var E=J(t,a,this.options.removeNSPrefix),y=E.tagName,P=E.rawTagName,A=E.tagExp,O=E.attrExpPresent,I=E.closeIndex;this.options.transformTagName&&(y=this.options.transformTagName(y)),r&&i&&"!xml"!==r.tagname&&(i=this.saveTextToParentTag(i,r,n,!1));var S=r;S&&-1!==this.options.unpairedTags.indexOf(S.tagname)&&(r=this.tagsNodeStack.pop(),n=n.substring(0,n.lastIndexOf("."))),y!==e.tagname&&(n+=n?"."+y:y);var C=a;if(this.isItStopNode(this.options.stopNodes,n,y)){var j="";if(A.length>0&&A.lastIndexOf("/")===A.length-1)"/"===y[y.length-1]?(y=y.substr(0,y.length-1),n=n.substr(0,n.length-1),A=y):A=A.substr(0,A.length-1),a=E.closeIndex;else if(-1!==this.options.unpairedTags.indexOf(y))a=E.closeIndex;else{var D=this.readStopNodeData(t,P,I+1);if(!D)throw new Error("Unexpected end of "+P);a=D.i,j=D.tagContent}var V=new T(y);y!==A&&O&&(V[":@"]=this.buildAttributesMap(A,n,y)),j&&(j=this.parseTextData(j,y,n,!0,O,!0,!0)),n=n.substr(0,n.lastIndexOf(".")),V.add(this.options.textNodeName,j),this.addChild(r,V,n,C)}else{if(A.length>0&&A.lastIndexOf("/")===A.length-1){"/"===y[y.length-1]?(y=y.substr(0,y.length-1),n=n.substr(0,n.length-1),A=y):A=A.substr(0,A.length-1),this.options.transformTagName&&(y=this.options.transformTagName(y));var M=new T(y);y!==A&&O&&(M[":@"]=this.buildAttributesMap(A,n,y)),this.addChild(r,M,n,C),n=n.substr(0,n.lastIndexOf("."))}else{var _=new T(y);this.tagsNodeStack.push(r),y!==A&&O&&(_[":@"]=this.buildAttributesMap(A,n,y)),this.addChild(r,_,n,C),r=_}i="",a=I}}else i+=t[a];return e.child};function $(t,e,r,i){this.options.captureMetaData||(i=void 0);var n=this.options.updateTag(e.tagname,r,e[":@"]);!1===n||("string"==typeof n?(e.tagname=n,t.addChild(e,i)):t.addChild(e,i))}var q=function(t){if(this.options.processEntities){for(var e in this.docTypeEntities){var r=this.docTypeEntities[e];t=t.replace(r.regx,r.val)}for(var i in this.lastEntities){var n=this.lastEntities[i];t=t.replace(n.regex,n.val)}if(this.options.htmlEntities)for(var a in this.htmlEntities){var s=this.htmlEntities[a];t=t.replace(s.regex,s.val)}t=t.replace(this.ampEntity.regex,this.ampEntity.val)}return t};function Z(t,e,r,i){return t&&(void 0===i&&(i=0===e.child.length),void 0!==(t=this.parseTextData(t,e.tagname,r,!1,!!e[":@"]&&0!==Object.keys(e[":@"]).length,i))&&""!==t&&e.add(this.options.textNodeName,t),t=""),t}function W(t,e,r){var i="*."+r;for(var n in t){var a=t[n];if(i===a||e===a)return!0}return!1}function z(t,e,r,i){var n=t.indexOf(e,r);if(-1===n)throw new Error(i);return n+e.length-1}function J(t,e,r,i){void 0===i&&(i=">");var n=function(t,e,r){var i;void 0===r&&(r=">");for(var n="",a=e;a<t.length;a++){var s=t[a];if(i)s===i&&(i="");else if('"'===s||"'"===s)i=s;else if(s===r[0]){if(!r[1])return{data:n,index:a};if(t[a+1]===r[1])return{data:n,index:a}}else"\t"===s&&(s=" ");n+=s}}(t,e+1,i);if(n){var a=n.data,s=n.index,o=a.search(/\s/),u=a,l=!0;-1!==o&&(u=a.substring(0,o),a=a.substring(o+1).trimStart());var h=u;if(r){var f=u.indexOf(":");-1!==f&&(l=(u=u.substr(f+1))!==n.data.substr(f+1))}return{tagName:u,tagExp:a,closeIndex:s,attrExpPresent:l,rawTagName:h}}}function H(t,e,r){for(var i=r,n=1;r<t.length;r++)if("<"===t[r])if("/"===t[r+1]){var a=z(t,">",r,e+" is not closed");if(t.substring(r+2,a).trim()===e&&0==--n)return{tagContent:t.substring(i,r),i:a};r=a}else if("?"===t[r+1])r=z(t,"?>",r+1,"StopNode is not closed.");else if("!--"===t.substr(r+1,3))r=z(t,"--\x3e",r+3,"StopNode is not closed.");else if("!["===t.substr(r+1,2))r=z(t,"]]>",r,"StopNode is not closed.")-2;else{var s=J(t,r,">");s&&((s&&s.tagName)===e&&"/"!==s.tagExp[s.tagExp.length-1]&&n++,r=s.closeIndex)}}function K(t,e,r){if(e&&"string"==typeof t){var i=t.trim();return"true"===i||"false"!==i&&function(t,e={}){if(e=Object.assign({},M,e),!t||"string"!=typeof t)return t;let r=t.trim();if(void 0!==e.skipLike&&e.skipLike.test(r))return t;if("0"===t)return 0;if(e.hex&&D.test(r))return function(t){if(parseInt)return parseInt(t,16);if(Number.parseInt)return Number.parseInt(t,16);if(window&&window.parseInt)return window.parseInt(t,16);throw new Error("parseInt, Number.parseInt, window.parseInt are not supported")}(r);if(-1!==r.search(/.+[eE].+/))return function(t,e,r){if(!r.eNotation)return t;const i=e.match(_);if(i){let n=i[1]||"";const a=-1===i[3].indexOf("e")?"E":"e",s=i[2],o=n?t[s.length+1]===a:t[s.length]===a;return s.length>1&&o?t:1!==s.length||!i[3].startsWith(`.${a}`)&&i[3][0]!==a?r.leadingZeros&&!o?(e=(i[1]||"")+i[3],Number(e)):t:Number(e)}return t}(t,r,e);{const n=V.exec(r);if(n){const a=n[1]||"",s=n[2];let o=(i=n[3])&&-1!==i.indexOf(".")?("."===(i=i.replace(/0+$/,""))?i="0":"."===i[0]?i="0"+i:"."===i[i.length-1]&&(i=i.substring(0,i.length-1)),i):i;const u=a?"."===t[s.length+1]:"."===t[s.length];if(!e.leadingZeros&&(s.length>1||1===s.length&&!u))return t;{const i=Number(r),n=String(i);if(0===i||-0===i)return i;if(-1!==n.search(/[eE]/))return e.eNotation?i:t;if(-1!==r.indexOf("."))return"0"===n||n===o||n===`${a}${o}`?i:t;let u=s?o:r;return s?u===n||a+u===n?i:t:u===n||u===a+n?i:t}}return t}var i}(t,r)}return void 0!==t?t:""}var Q=T.getMetaDataSymbol();function tt(t,e){return et(t,e)}function et(t,e,r){for(var i,n={},a=0;a<t.length;a++){var s,o=t[a],u=rt(o);if(s=void 0===r?u:r+"."+u,u===e.textNodeName)void 0===i?i=o[u]:i+=""+o[u];else{if(void 0===u)continue;if(o[u]){var l=et(o[u],e,s),h=nt(l,e);void 0!==o[Q]&&(l[Q]=o[Q]),o[":@"]?it(l,o[":@"],s,e):1!==Object.keys(l).length||void 0===l[e.textNodeName]||e.alwaysCreateTextNode?0===Object.keys(l).length&&(e.alwaysCreateTextNode?l[e.textNodeName]="":l=""):l=l[e.textNodeName],void 0!==n[u]&&n.hasOwnProperty(u)?(Array.isArray(n[u])||(n[u]=[n[u]]),n[u].push(l)):e.isArray(u,s,h)?n[u]=[l]:n[u]=l}}}return"string"==typeof i?i.length>0&&(n[e.textNodeName]=i):void 0!==i&&(n[e.textNodeName]=i),n}function rt(t){for(var e=Object.keys(t),r=0;r<e.length;r++){var i=e[r];if(":@"!==i)return i}}function it(t,e,r,i){if(e)for(var n=Object.keys(e),a=n.length,s=0;s<a;s++){var o=n[s];i.isArray(o,r+"."+o,!0,!0)?t[o]=[e[o]]:t[o]=e[o]}}function nt(t,e){var r=e.textNodeName,i=Object.keys(t).length;return 0===i||!(1!==i||!t[r]&&"boolean"!=typeof t[r]&&0!==t[r])}var at=function(){function t(t){this.externalEntities={},this.options=function(t){return Object.assign({},y,t)}(t)}var e=t.prototype;return e.parse=function(t,e){if("string"==typeof t);else{if(!t.toString)throw new Error("XML data is accepted in String or Bytes[] form.");t=t.toString()}if(e){!0===e&&(e={});var r=o(t,e);if(!0!==r)throw Error(r.err.msg+":"+r.err.line+":"+r.err.col)}var i=new L(this.options);i.addExternalEntities(this.externalEntities);var n=i.parseXml(t);return this.options.preserveOrder||void 0===n?n:tt(n,this.options)},e.addEntity=function(t,e){if(-1!==e.indexOf("&"))throw new Error("Entity value can't have '&'");if(-1!==t.indexOf("&")||-1!==t.indexOf(";"))throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if("&"===e)throw new Error("An entity with value '&' is not permitted");this.externalEntities[t]=e},t.getMetaDataSymbol=function(){return T.getMetaDataSymbol()},t}();function st(t,e){var r="";return e.format&&e.indentBy.length>0&&(r="\n"),ot(t,e,"",r)}function ot(t,e,r,i){for(var n="",a=!1,s=0;s<t.length;s++){var o=t[s],u=ut(o);if(void 0!==u){var l;if(l=0===r.length?u:r+"."+u,u!==e.textNodeName)if(u!==e.cdataPropName)if(u!==e.commentPropName)if("?"!==u[0]){var h=i;""!==h&&(h+=e.indentBy);var f=i+"<"+u+lt(o[":@"],e),d=ot(o[u],e,l,h);-1!==e.unpairedTags.indexOf(u)?e.suppressUnpairedNode?n+=f+">":n+=f+"/>":d&&0!==d.length||!e.suppressEmptyNode?d&&d.endsWith(">")?n+=f+">"+d+i+"</"+u+">":(n+=f+">",d&&""!==i&&(d.includes("/>")||d.includes("</"))?n+=i+e.indentBy+d+i:n+=d,n+="</"+u+">"):n+=f+"/>",a=!0}else{var p=lt(o[":@"],e),g="?xml"===u?"":i,c=o[u][0][e.textNodeName];n+=g+"<"+u+(c=0!==c.length?" "+c:"")+p+"?>",a=!0}else n+=i+"\x3c!--"+o[u][0][e.textNodeName]+"--\x3e",a=!0;else a&&(n+=i),n+="<![CDATA["+o[u][0][e.textNodeName]+"]]>",a=!1;else{var v=o[u];ht(l,e)||(v=ft(v=e.tagValueProcessor(u,v),e)),a&&(n+=i),n+=v,a=!1}}}return n}function ut(t){for(var e=Object.keys(t),r=0;r<e.length;r++){var i=e[r];if(t.hasOwnProperty(i)&&":@"!==i)return i}}function lt(t,e){var r="";if(t&&!e.ignoreAttributes)for(var i in t)if(t.hasOwnProperty(i)){var n=e.attributeValueProcessor(i,t[i]);!0===(n=ft(n,e))&&e.suppressBooleanAttributes?r+=" "+i.substr(e.attributeNamePrefix.length):r+=" "+i.substr(e.attributeNamePrefix.length)+'="'+n+'"'}return r}function ht(t,e){var r=(t=t.substr(0,t.length-e.textNodeName.length-1)).substr(t.lastIndexOf(".")+1);for(var i in e.stopNodes)if(e.stopNodes[i]===t||e.stopNodes[i]==="*."+r)return!0;return!1}function ft(t,e){if(t&&t.length>0&&e.processEntities)for(var r=0;r<e.entities.length;r++){var i=e.entities[r];t=t.replace(i.regex,i.val)}return t}var dt={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(t,e){return e},attributeValueProcessor:function(t,e){return e},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function pt(t){this.options=Object.assign({},dt,t),!0===this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.ignoreAttributesFn=F(this.options.ignoreAttributes),this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=vt),this.processTextOrObjNode=gt,this.options.format?(this.indentate=ct,this.tagEndChar=">\n",this.newLine="\n"):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}function gt(t,e,r,i){var n=this.j2x(t,r+1,i.concat(e));return void 0!==t[this.options.textNodeName]&&1===Object.keys(t).length?this.buildTextValNode(t[this.options.textNodeName],e,n.attrStr,r):this.buildObjectNode(n.val,e,n.attrStr,r)}function ct(t){return this.options.indentBy.repeat(t)}function vt(t){return!(!t.startsWith(this.options.attributeNamePrefix)||t===this.options.textNodeName)&&t.substr(this.attrPrefixLen)}pt.prototype.build=function(t){return this.options.preserveOrder?st(t,this.options):(Array.isArray(t)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&((e={})[this.options.arrayNodeName]=t,t=e),this.j2x(t,0,[]).val);var e},pt.prototype.j2x=function(t,e,r){var i="",n="",a=r.join(".");for(var s in t)if(Object.prototype.hasOwnProperty.call(t,s))if(void 0===t[s])this.isAttribute(s)&&(n+="");else if(null===t[s])this.isAttribute(s)||s===this.options.cdataPropName?n+="":"?"===s[0]?n+=this.indentate(e)+"<"+s+"?"+this.tagEndChar:n+=this.indentate(e)+"<"+s+"/"+this.tagEndChar;else if(t[s]instanceof Date)n+=this.buildTextValNode(t[s],s,"",e);else if("object"!=typeof t[s]){var o=this.isAttribute(s);if(o&&!this.ignoreAttributesFn(o,a))i+=this.buildAttrPairStr(o,""+t[s]);else if(!o)if(s===this.options.textNodeName){var u=this.options.tagValueProcessor(s,""+t[s]);n+=this.replaceEntitiesValue(u)}else n+=this.buildTextValNode(t[s],s,"",e)}else if(Array.isArray(t[s])){for(var l=t[s].length,h="",f="",d=0;d<l;d++){var p=t[s][d];if(void 0===p);else if(null===p)"?"===s[0]?n+=this.indentate(e)+"<"+s+"?"+this.tagEndChar:n+=this.indentate(e)+"<"+s+"/"+this.tagEndChar;else if("object"==typeof p)if(this.options.oneListGroup){var g=this.j2x(p,e+1,r.concat(s));h+=g.val,this.options.attributesGroupName&&p.hasOwnProperty(this.options.attributesGroupName)&&(f+=g.attrStr)}else h+=this.processTextOrObjNode(p,s,e,r);else if(this.options.oneListGroup){var c=this.options.tagValueProcessor(s,p);h+=c=this.replaceEntitiesValue(c)}else h+=this.buildTextValNode(p,s,"",e)}this.options.oneListGroup&&(h=this.buildObjectNode(h,s,f,e)),n+=h}else if(this.options.attributesGroupName&&s===this.options.attributesGroupName)for(var v=Object.keys(t[s]),m=v.length,x=0;x<m;x++)i+=this.buildAttrPairStr(v[x],""+t[s][v[x]]);else n+=this.processTextOrObjNode(t[s],s,e,r);return{attrStr:i,val:n}},pt.prototype.buildAttrPairStr=function(t,e){return e=this.options.attributeValueProcessor(t,""+e),e=this.replaceEntitiesValue(e),this.options.suppressBooleanAttributes&&"true"===e?" "+t:" "+t+'="'+e+'"'},pt.prototype.buildObjectNode=function(t,e,r,i){if(""===t)return"?"===e[0]?this.indentate(i)+"<"+e+r+"?"+this.tagEndChar:this.indentate(i)+"<"+e+r+this.closeTag(e)+this.tagEndChar;var n="</"+e+this.tagEndChar,a="";return"?"===e[0]&&(a="?",n=""),!r&&""!==r||-1!==t.indexOf("<")?!1!==this.options.commentPropName&&e===this.options.commentPropName&&0===a.length?this.indentate(i)+"\x3c!--"+t+"--\x3e"+this.newLine:this.indentate(i)+"<"+e+r+a+this.tagEndChar+t+this.indentate(i)+n:this.indentate(i)+"<"+e+r+a+">"+t+n},pt.prototype.closeTag=function(t){var e="";return-1!==this.options.unpairedTags.indexOf(t)?this.options.suppressUnpairedNode||(e="/"):e=this.options.suppressEmptyNode?"/":"></"+t,e},pt.prototype.buildTextValNode=function(t,e,r,i){if(!1!==this.options.cdataPropName&&e===this.options.cdataPropName)return this.indentate(i)+"<![CDATA["+t+"]]>"+this.newLine;if(!1!==this.options.commentPropName&&e===this.options.commentPropName)return this.indentate(i)+"\x3c!--"+t+"--\x3e"+this.newLine;if("?"===e[0])return this.indentate(i)+"<"+e+r+"?"+this.tagEndChar;var n=this.options.tagValueProcessor(e,t);return""===(n=this.replaceEntitiesValue(n))?this.indentate(i)+"<"+e+r+this.closeTag(e)+this.tagEndChar:this.indentate(i)+"<"+e+r+">"+n+"</"+e+this.tagEndChar},pt.prototype.replaceEntitiesValue=function(t){if(t&&t.length>0&&this.options.processEntities)for(var e=0;e<this.options.entities.length;e++){var r=this.options.entities[e];t=t.replace(r.regex,r.val)}return t};var mt={validate:o};return e})()));
//# sourceMappingURL=fxp.min.js.map